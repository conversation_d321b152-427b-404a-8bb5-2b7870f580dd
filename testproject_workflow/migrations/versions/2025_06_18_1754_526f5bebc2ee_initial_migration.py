""""Initial_migration"

Revision ID: 526f5bebc2ee
Revises: 
Create Date: 2025-06-18 17:54:06.042122+00:00

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel
import fabiplus.core.user_model


# revision identifiers, used by Alembic.
revision = "526f5bebc2ee"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "category",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.<PERSON>onstraint("id"),
    )
    op.create_table(
        "post",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("title", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "content", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("is_published", sa.Boolean(), nullable=False),
        sa.Column("author", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "user",
        sa.Column("id", fabiplus.core.user_model.GUID(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("(CURRENT_TIMESTAMP)"),
            nullable=True,
        ),
        sa.Column(
            "username", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("email", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column(
            "first_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "last_name", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("is_staff", sa.Boolean(), nullable=False),
        sa.Column("is_superuser", sa.Boolean(), nullable=False),
        sa.Column(
            "hashed_password",
            sqlmodel.sql.sqltypes.AutoString(),
            nullable=False,
        ),
        sa.Column("last_login", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_user_email"), "user", ["email"], unique=True)
    op.create_index(
        op.f("ix_user_username"), "user", ["username"], unique=True
    )
    op.create_table(
        "activities",
        sa.Column("id", sqlmodel.sql.sqltypes.GUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column(
            "activity_type",
            sa.Enum(
                "CREATE",
                "READ",
                "UPDATE",
                "DELETE",
                "LOGIN",
                "LOGOUT",
                "ADMIN_ACCESS",
                "API_CALL",
                "ERROR",
                "SYSTEM",
                name="activitytype",
            ),
            nullable=False,
        ),
        sa.Column(
            "level",
            sa.Enum("LOW", "NORMAL", "HIGH", "CRITICAL", name="activitylevel"),
            nullable=False,
        ),
        sa.Column(
            "action", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column(
            "description", sqlmodel.sql.sqltypes.AutoString(), nullable=False
        ),
        sa.Column("user_id", sqlmodel.sql.sqltypes.GUID(), nullable=True),
        sa.Column(
            "user_email", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "user_ip", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "user_agent", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_type", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column(
            "object_repr", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.Column("method", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("path", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("status_code", sa.Integer(), nullable=True),
        sa.Column("response_time", sa.Float(), nullable=True),
        sa.Column(
            "extra_data", sqlmodel.sql.sqltypes.AutoString(), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_activities_action"), "activities", ["action"], unique=False
    )
    op.create_index(
        op.f("ix_activities_activity_type"),
        "activities",
        ["activity_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_level"), "activities", ["level"], unique=False
    )
    op.create_index(
        op.f("ix_activities_object_id"),
        "activities",
        ["object_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_object_type"),
        "activities",
        ["object_type"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_status_code"),
        "activities",
        ["status_code"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_timestamp"),
        "activities",
        ["timestamp"],
        unique=False,
    )
    op.create_index(
        op.f("ix_activities_user_id"), "activities", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_activities_user_id"), table_name="activities")
    op.drop_index(op.f("ix_activities_timestamp"), table_name="activities")
    op.drop_index(op.f("ix_activities_status_code"), table_name="activities")
    op.drop_index(op.f("ix_activities_object_type"), table_name="activities")
    op.drop_index(op.f("ix_activities_object_id"), table_name="activities")
    op.drop_index(op.f("ix_activities_level"), table_name="activities")
    op.drop_index(op.f("ix_activities_activity_type"), table_name="activities")
    op.drop_index(op.f("ix_activities_action"), table_name="activities")
    op.drop_table("activities")
    op.drop_index(op.f("ix_user_username"), table_name="user")
    op.drop_index(op.f("ix_user_email"), table_name="user")
    op.drop_table("user")
    op.drop_table("post")
    op.drop_table("category")
    # ### end Alembic commands ###
