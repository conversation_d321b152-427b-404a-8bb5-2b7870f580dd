"""
Blog Models
Database models for the blog app (SQLModel backend)
"""

from typing import Optional
from sqlmodel import Field
from fabiplus.core.models import BaseModel, register_model


@register_model
class Post(BaseModel, table=True):
    """Blog post model"""

    title: str = Field(max_length=200, description="Post title")
    content: str = Field(description="Post content")
    is_published: bool = Field(default=False, description="Is published")
    author: Optional[str] = Field(default=None, max_length=100, description="Author name")

    class Config:
        _verbose_name = "Blog Post"
        _verbose_name_plural = "Blog Posts"

    def __str__(self):
        return self.title


@register_model
class Category(BaseModel, table=True):
    """Blog category model"""

    name: str = Field(max_length=100, description="Category name")
    description: Optional[str] = Field(default="", description="Category description")
    is_active: bool = Field(default=True, description="Is active")

    class Config:
        _verbose_name = "Category"
        _verbose_name_plural = "Categories"

    def __str__(self):
        return self.name